# 简单的编码修复脚本
$sourceDir = "yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java"

# 获取所有Java文件
$javaFiles = Get-ChildItem -Path $sourceDir -Filter "*.java" -Recurse

Write-Host "找到 $($javaFiles.Count) 个Java文件"

foreach ($file in $javaFiles) {
    Write-Host "处理文件: $($file.FullName)"
    
    try {
        # 读取文件内容（尝试多种编码）
        $content = $null
        
        # 尝试UTF-8编码读取
        try {
            $content = Get-Content -Path $file.FullName -Encoding UTF8 -Raw
        } catch {
            # 如果UTF-8失败，尝试默认编码
            try {
                $content = Get-Content -Path $file.FullName -Raw
            } catch {
                Write-Host "无法读取文件: $($file.FullName)"
                continue
            }
        }
        
        # 检查是否包含乱码字符
        if ($content -match '[鐠閻绠璁娴鈻閮铏鐢宠鑱郴浜虹璁]') {
            Write-Host "发现编码问题，正在修复..."
            
            # 基本的字符替换
            $content = $content -replace '鐠佸灝濞翠胶鈻?', '访客流程'
            $content = $content -replace '璁垮', '访客'
            $content = $content -replace '鍩硅', '培训'
            $content = $content -replace '璁板綍', '记录'
            $content = $content -replace '鐢宠', '申请'
            $content = $content -replace '鍒涘缓', '创建'
            $content = $content -replace '鏇存柊', '更新'
            $content = $content -replace '鍒犻櫎', '删除'
            $content = $content -replace '鑾峰緱', '获得'
            $content = $content -replace '鍒楄〃', '列表'
            $content = $content -replace '鍒嗛〉', '分页'
            $content = $content -replace '鏌ヨ', '查询'
            $content = $content -replace '鏉′欢', '条件'
            $content = $content -replace '瀵煎嚭', '导出'
            $content = $content -replace '缂栧彿', '编号'
            $content = $content -replace '绠＄悊鍚庡彴', '管理后台'
            $content = $content -replace '瀹炵幇绫?', '实现类'
            $content = $content -replace '鎺ュ彛', '接口'
            $content = $content -replace '鍙傛暟', '参数'
            $content = $content -replace '鎴愬姛', '成功'
            $content = $content -replace '澶辫触', '失败'
            $content = $content -replace '寮€濮?', '开始'
            $content = $content -replace '瀹屾垚', '完成'
            $content = $content -replace '妫€鏌?', '检查'
            $content = $content -replace '楠岃瘉', '验证'
            $content = $content -replace '瀛樺湪', '存在'
            $content = $content -replace '涓嶅瓨鍦?', '不存在'
            $content = $content -replace '涓嶈兘涓虹┖', '不能为空'
            $content = $content -replace '蹇呬慨', '必修'
            $content = $content -replace '閫夋嫨', '选择'
            $content = $content -replace '绫诲瀷', '类型'
            $content = $content -replace '鍚嶇О', '名称'
            $content = $content -replace '鎻忚堪', '描述'
            $content = $content -replace '鐘舵€?', '状态'
            $content = $content -replace '鏃堕棿', '时间'
            $content = $content -replace '鍦板潃', '地址'
            $content = $content -replace '鐢佃瘽', '电话'
            $content = $content -replace '濮撳悕', '姓名'
            $content = $content -replace '鍏徃', '公司'
            $content = $content -replace '閮ㄩ棬', '部门'
            $content = $content -replace '鑱旂郴浜?', '联系人'
            $content = $content -replace '韬唤璇?', '身份证'
            $content = $content -replace '鐓х墖', '照片'
            $content = $content -replace '杞︾墝', '车牌'
            $content = $content -replace '杞﹁締', '车辆'
            $content = $content -replace '椹鹃┒璇?', '驾驶证'
            $content = $content -replace '琛岄┒璇?', '行驶证'
            $content = $content -replace '鍚岃浜?', '同行人'
            $content = $content -replace '绱ф€ヨ仈绯讳汉', '紧急联系人'
            $content = $content -replace '鐗规畩瑕佹眰', '特殊要求'
            $content = $content -replace '浣忓', '住宿'
            $content = $content -replace '灏遍', '就餐'
            $content = $content -replace '楗爞', '饭堂'
            $content = $content -replace '鍘傚尯', '厂区'
            $content = $content -replace '闂ㄥ矖', '门岗'
            $content = $content -replace '鍏ュ洯', '入园'
            $content = $content -replace '鍑哄洯', '出园'
            $content = $content -replace '杩涘嚭', '进出'
            $content = $content -replace '娓╁害', '温度'
            $content = $content -replace '鍋ュ悍', '健康'
            $content = $content -replace '瀹夊叏', '安全'
            $content = $content -replace '妫€鏌?', '检查'
            $content = $content -replace '閫氳繃', '通过'
            $content = $content -replace '姝ｅ父', '正常'
            $content = $content -replace '寮傚父', '异常'
            $content = $content -replace '澶囨敞', '备注'
            $content = $content -replace '鎿嶄綔鍛?', '操作员'
            $content = $content -replace '缁熻', '统计'
            $content = $content -replace '鑰冭瘯', '考试'
            $content = $content -replace '鍒嗘暟', '分数'
            $content = $content -replace '鏃堕暱', '时长'
            $content = $content -replace '鍒嗛挓', '分钟'
            $content = $content -replace '杩涘害', '进度'
            $content = $content -replace '淇℃伅', '信息'
            $content = $content -replace '鐧昏', '登记'
            $content = $content -replace '浜岀淮鐮?', '二维码'
            $content = $content -replace '榛樿', '默认'
            $content = $content -replace '鏍￠獙', '校验'
            $content = $content -replace '璁＄畻', '计算'
            $content = $content -replace '鏈€灏?', '最小'
            $content = $content -replace '鏈€澶?', '最大'
            $content = $content -replace '澶ぇ', '太大'
            $content = $content -replace '澶皬', '太小'
            $content = $content -replace '澶暱', '太长'
            $content = $content -replace '澶煭', '太短'
            
            # 保存修复后的文件
            $content | Out-File -FilePath $file.FullName -Encoding UTF8 -NoNewline
            Write-Host "文件修复完成: $($file.Name)"
        }
        
    } catch {
        Write-Host "处理文件时出错: $($file.FullName) - $($_.Exception.Message)"
    }
}

Write-Host "编码修复完成！"