﻿package cn.iocoder.yudao.module.visitor.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.visitor.service.application.VisitorApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 访客申请过期处理定时任务
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Component
@Slf4j
public class VisitorExpireJob implements JobHandler {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        log.info("[execute] 开始执行访客申请过期处理定时任务");

        try {
            // 处理过期的访客申请
            visitorApplicationService.handleExpiredApplications();
            
            log.info("[execute] 访客申请过期处理定时任务执行完成");
            return "访客申请过期处理完成";
        } catch (Exception e) {
            log.error("[execute] 访客申请过期处理定时任务执行失败", e);
            throw e;
        }
    }

}

