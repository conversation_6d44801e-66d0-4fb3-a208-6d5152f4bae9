package cn.iocoder.yudao.module.visitor.controller.admin.process.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 访客流程异常处理记录分页 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客流程异常处理记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitorProcessExceptionPageReqVO extends PageParam {

    @Schema(description = "申请单ID", example = "1024")
    private Long applicationId;

    @Schema(description = "异常类型", example = "1")
    private Integer exceptionType;

    @Schema(description = "异常级别", example = "2")
    private Integer exceptionLevel;

    @Schema(description = "处理状态?, example = "1")
    private Integer handlingStatus;

    @Schema(description = "处理人ID", example = "1")
    private Long handlerId;

    @Schema(description = "异常鍙戠敓时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] exceptionTime;

}

