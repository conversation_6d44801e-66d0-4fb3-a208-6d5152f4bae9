﻿package cn.iocoder.yudao.module.visitor.controller.admin.application.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 管理后台 - 访客申请鍙栨秷 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客申请鍙栨秷 Request VO")
@Data
public class VisitorApplicationCancelReqVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "申请ID不能为空")
    private Long id;

    @Schema(description = "鍙栨秷鍘熷洜", requiredMode = Schema.RequiredMode.REQUIRED, example = "访客涓存椂鏈変簨鏃犳硶鍒拌")
    @NotBlank(message = "鍙栨秷鍘熷洜不能为空")
    private String reason;

}

