@echo off
chcp 65001
echo 开始修复Java文件编码问题...

cd yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java

for /r %%f in (*.java) do (
    echo 处理文件: %%f
    powershell -Command "(Get-Content '%%f' -Raw -Encoding UTF8) -replace '璁垮', '访客' -replace '鍩硅', '培训' -replace '璁板綍', '记录' -replace '鐢宠', '申请' -replace '鍒涘缓', '创建' -replace '鏇存柊', '更新' -replace '鍒犻櫎', '删除' -replace '鑾峰緱', '获得' -replace '鍒楄〃', '列表' -replace '鍒嗛〉', '分页' -replace '鏌ヨ', '查询' -replace '鏉′欢', '条件' -replace '瀵煎嚭', '导出' -replace '缂栧彿', '编号' -replace '绠＄悊鍚庡彴', '管理后台' -replace '瀹炵幇绫?', '实现类' -replace '鎺ュ彛', '接口' -replace '鍙傛暟', '参数' -replace '鎴愬姛', '成功' -replace '澶辫触', '失败' -replace '寮€濮?', '开始' -replace '瀹屾垚', '完成' -replace '妫€鏌?', '检查' -replace '楠岃瘉', '验证' -replace '瀛樺湪', '存在' -replace '涓嶅瓨鍦?', '不存在' -replace '涓嶈兘涓虹┖', '不能为空' -replace '蹇呬慨', '必修' -replace '閫夋嫨', '选择' -replace '绫诲瀷', '类型' -replace '鍚嶇О', '名称' -replace '鎻忚堪', '描述' -replace '鐘舵€?', '状态' -replace '鏃堕棿', '时间' -replace '鍦板潃', '地址' -replace '鐢佃瘽', '电话' -replace '濮撳悕', '姓名' -replace '鍏徃', '公司' -replace '閮ㄩ棬', '部门' -replace '鑱旂郴浜?', '联系人' -replace '韬唤璇?', '身份证' -replace '鐓х墖', '照片' -replace '杞︾墝', '车牌' -replace '杞﹁締', '车辆' -replace '椹鹃┒璇?', '驾驶证' -replace '琛岄┒璇?', '行驶证' -replace '鍚岃浜?', '同行人' -replace '绱ф€ヨ仈绯讳汉', '紧急联系人' -replace '鐗规畩瑕佹眰', '特殊要求' -replace '浣忓', '住宿' -replace '灏遍', '就餐' -replace '楗爞', '饭堂' -replace '鍘傚尯', '厂区' -replace '闂ㄥ矖', '门岗' -replace '鍏ュ洯', '入园' -replace '鍑哄洯', '出园' -replace '杩涘嚭', '进出' -replace '娓╁害', '温度' -replace '鍋ュ悍', '健康' -replace '瀹夊叏', '安全' -replace '妫€鏌?', '检查' -replace '閫氳繃', '通过' -replace '姝ｅ父', '正常' -replace '寮傚父', '异常' -replace '澶囨敞', '备注' -replace '鎿嶄綔鍛?', '操作员' -replace '缁熻', '统计' -replace '鑰冭瘯', '考试' -replace '鍒嗘暟', '分数' -replace '鏃堕暱', '时长' -replace '鍒嗛挓', '分钟' -replace '杩涘害', '进度' -replace '淇℃伅', '信息' -replace '鐧昏', '登记' -replace '浜岀淮鐮?', '二维码' -replace '榛樿', '默认' -replace '鏍￠獙', '校验' -replace '璁＄畻', '计算' -replace '鏈€灏?', '最小' -replace '鏈€澶?', '最大' -replace '澶ぇ', '太大' -replace '澶皬', '太小' -replace '澶暱', '太长' -replace '澶煭', '太短' -replace '娌℃湁', '没有' -replace '宸插瓨鍦?', '已存在' -replace '宸插畬鎴?', '已完成' -replace '鏈畬鎴?', '未完成' -replace '鍚敤', '启用' -replace '绂佺敤', '禁用' | Set-Content '%%f' -Encoding UTF8"
)

cd ..\..\..\..\..\..

echo 编码修复完成！
pause