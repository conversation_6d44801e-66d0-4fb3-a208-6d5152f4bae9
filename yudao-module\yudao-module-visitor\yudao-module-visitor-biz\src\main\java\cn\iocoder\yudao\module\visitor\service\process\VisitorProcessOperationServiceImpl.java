﻿package cn.iocoder.yudao.module.visitor.service.process;

import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorProcessOperationDO;
import cn.iocoder.yudao.module.visitor.dal.mysql.VisitorProcessOperationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 访客娴佺▼操作员记录 Service 实现类? *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Service
@Validated
@Slf4j
public class VisitorProcessOperationServiceImpl implements VisitorProcessOperationService {

    @Resource
    private VisitorProcessOperationMapper processOperationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createProcessOperation(VisitorProcessOperationDO operation) {
        log.info("[createProcessOperation] 创建娴佺▼操作员记录锛岀敵璇稩D锛歿}", operation.getApplicationId());
        
        processOperationMapper.insert(operation);
        
        log.info("[createProcessOperation] 娴佺▼操作员记录创建成功锛孖D锛歿}", operation.getId());
        return operation.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long recordOperation(Long applicationId, String processInstanceId, String taskId, 
                               Integer operationType, Integer operationResult, Long operatorId, 
                               String operatorName, String operationContent, String operationReason) {
        log.info("[recordOperation] 记录娴佺▼操作员锛岀敵璇稩D锛歿}锛屾搷浣滅被鍨嬶細{}", applicationId, operationType);
        
        VisitorProcessOperationDO operation = VisitorProcessOperationDO.builder()
                .applicationId(applicationId)
                .processInstanceId(processInstanceId)
                .taskId(taskId)
                .operationType(operationType)
                .operationResult(operationResult)
                .operatorId(operatorId)
                .operatorName(operatorName)
                .operationTime(LocalDateTime.now())
                .operationContent(operationContent)
                .operationReason(operationReason)
                .build();
        
        processOperationMapper.insert(operation);
        
        log.info("[recordOperation] 娴佺▼操作员记录成功锛孖D锛歿}", operation.getId());
        return operation.getId();
    }

    @Override
    public VisitorProcessOperationDO getProcessOperation(Long id) {
        return processOperationMapper.selectById(id);
    }

    @Override
    public List<VisitorProcessOperationDO> getOperationsByApplicationId(Long applicationId) {
        return processOperationMapper.selectListByApplicationId(applicationId);
    }

    @Override
    public List<VisitorProcessOperationDO> getOperationsByProcessInstanceId(String processInstanceId) {
        return processOperationMapper.selectListByProcessInstanceId(processInstanceId);
    }

    @Override
    public List<VisitorProcessOperationDO> getOperationsByType(Integer operationType, LocalDateTime startTime, LocalDateTime endTime) {
        return processOperationMapper.selectListByType(operationType, startTime, endTime);
    }

    @Override
    public Object getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("[getOperationStatistics] 鑾峰彇操作员统计锛屾椂闂磋寖鍥达細{} - {}", startTime, endTime);
        
        // 统计鍚勭操作员类型鐨勬暟閲?        Map<String, Long> statistics = new HashMap<>();
        
        // 统计鎻愪氦申请鏁伴噺
        statistics.put("submitCount", processOperationMapper.selectCountByType(1, startTime, endTime));
        // 统计联系人虹‘璁ゆ暟閲?        statistics.put("contactConfirmCount", processOperationMapper.selectCountByType(2, startTime, endTime));
        // 统计部门瀹℃壒鏁伴噺
        statistics.put("deptApprovalCount", processOperationMapper.selectCountByType(3, startTime, endTime));
        // 统计缁肩閮ㄥ鎵规暟閲?        statistics.put("finalApprovalCount", processOperationMapper.selectCountByType(4, startTime, endTime));
        // 统计入园登记鏁伴噺
        statistics.put("entryCount", processOperationMapper.selectCountByType(7, startTime, endTime));
        // 统计出园登记鏁伴噺
        statistics.put("exitCount", processOperationMapper.selectCountByType(8, startTime, endTime));
        
        return statistics;
    }

}

