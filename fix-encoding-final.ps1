# 修复Java文件编码问题
$sourceDir = "yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java"

# 获取所有Java文件
$javaFiles = Get-ChildItem -Path $sourceDir -Filter "*.java" -Recurse

Write-Host "找到 $($javaFiles.Count) 个Java文件"

foreach ($file in $javaFiles) {
    Write-Host "处理文件: $($file.Name)"
    
    try {
        # 读取文件内容
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # 检查是否包含乱码字符
        if ($content -match '[鐠閻绠璁娴鈻閮铏鐢宠鑱郴浜虹璁]') {
            Write-Host "发现编码问题，正在修复..."
            
            # 替换常见的乱码
            $replacements = @{
                '璁垮' = '访客'
                '鍩硅' = '培训'
                '璁板綍' = '记录'
                '鐢宠' = '申请'
                '鍒涘缓' = '创建'
                '鏇存柊' = '更新'
                '鍒犻櫎' = '删除'
                '鑾峰緱' = '获得'
                '鍒楄〃' = '列表'
                '鍒嗛〉' = '分页'
                '鏌ヨ' = '查询'
                '鏉′欢' = '条件'
                '瀵煎嚭' = '导出'
                '缂栧彿' = '编号'
                '绠＄悊鍚庡彴' = '管理后台'
                '瀹炵幇绫?' = '实现类'
                '鎺ュ彛' = '接口'
                '鍙傛暟' = '参数'
                '鎴愬姛' = '成功'
                '澶辫触' = '失败'
                '寮€濮?' = '开始'
                '瀹屾垚' = '完成'
                '妫€鏌?' = '检查'
                '楠岃瘉' = '验证'
                '瀛樺湪' = '存在'
                '涓嶅瓨鍦?' = '不存在'
                '涓嶈兘涓虹┖' = '不能为空'
                '蹇呬慨' = '必修'
                '閫夋嫨' = '选择'
                '绫诲瀷' = '类型'
                '鍚嶇О' = '名称'
                '鎻忚堪' = '描述'
                '鐘舵€?' = '状态'
                '鏃堕棿' = '时间'
                '鍦板潃' = '地址'
                '鐢佃瘽' = '电话'
                '濮撳悕' = '姓名'
                '鍏徃' = '公司'
                '閮ㄩ棬' = '部门'
                '鑱旂郴浜?' = '联系人'
                '韬唤璇?' = '身份证'
                '鐓х墖' = '照片'
                '杞︾墝' = '车牌'
                '杞﹁締' = '车辆'
                '椹鹃┒璇?' = '驾驶证'
                '琛岄┒璇?' = '行驶证'
                '鍚岃浜?' = '同行人'
                '绱ф€ヨ仈绯讳汉' = '紧急联系人'
                '鐗规畩瑕佹眰' = '特殊要求'
                '浣忓' = '住宿'
                '灏遍' = '就餐'
                '楗爞' = '饭堂'
                '鍘傚尯' = '厂区'
                '闂ㄥ矖' = '门岗'
                '鍏ュ洯' = '入园'
                '鍑哄洯' = '出园'
                '杩涘嚭' = '进出'
                '娓╁害' = '温度'
                '鍋ュ悍' = '健康'
                '瀹夊叏' = '安全'
                '妫€鏌?' = '检查'
                '閫氳繃' = '通过'
                '姝ｅ父' = '正常'
                '寮傚父' = '异常'
                '澶囨敞' = '备注'
                '鎿嶄綔鍛?' = '操作员'
                '缁熻' = '统计'
                '鑰冭瘯' = '考试'
                '鍒嗘暟' = '分数'
                '鏃堕暱' = '时长'
                '鍒嗛挓' = '分钟'
                '杩涘害' = '进度'
                '淇℃伅' = '信息'
                '鐧昏' = '登记'
                '浜岀淮鐮?' = '二维码'
                '榛樿' = '默认'
                '鏍￠獙' = '校验'
                '璁＄畻' = '计算'
                '鏈€灏?' = '最小'
                '鏈€澶?' = '最大'
                '澶ぇ' = '太大'
                '澶皬' = '太小'
                '澶暱' = '太长'
                '澶煭' = '太短'
            }
            
            # 执行替换
            foreach ($key in $replacements.Keys) {
                $content = $content -replace [regex]::Escape($key), $replacements[$key]
            }
            
            # 保存修复后的文件
            $content | Out-File -FilePath $file.FullName -Encoding UTF8 -NoNewline
            Write-Host "文件修复完成: $($file.Name)"
        }
        
    } catch {
        Write-Host "处理文件时出错: $($file.FullName) - $($_.Exception.Message)"
    }
}

Write-Host "编码修复完成！"