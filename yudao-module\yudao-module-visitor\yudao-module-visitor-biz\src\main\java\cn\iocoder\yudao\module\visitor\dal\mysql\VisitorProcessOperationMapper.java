﻿package cn.iocoder.yudao.module.visitor.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.visitor.controller.admin.process.vo.VisitorProcessOperationPageReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorProcessOperationDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 访客娴佺▼操作员记录 Mapper
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Mapper
public interface VisitorProcessOperationMapper extends BaseMapperX<VisitorProcessOperationDO> {

    default PageResult<VisitorProcessOperationDO> selectPage(VisitorProcessOperationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VisitorProcessOperationDO>()
                .eqIfPresent(VisitorProcessOperationDO::getApplicationId, reqVO.getApplicationId())
                .eqIfPresent(VisitorProcessOperationDO::getProcessInstanceId, reqVO.getProcessInstanceId())
                .eqIfPresent(VisitorProcessOperationDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(VisitorProcessOperationDO::getOperationType, reqVO.getOperationType())
                .eqIfPresent(VisitorProcessOperationDO::getOperationResult, reqVO.getOperationResult())
                .eqIfPresent(VisitorProcessOperationDO::getOperatorId, reqVO.getOperatorId())
                .likeIfPresent(VisitorProcessOperationDO::getOperatorName, reqVO.getOperatorName())
                .eqIfPresent(VisitorProcessOperationDO::getOperatorType, reqVO.getOperatorType())
                .betweenIfPresent(VisitorProcessOperationDO::getOperationTime, reqVO.getOperationTime())
                .betweenIfPresent(VisitorProcessOperationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VisitorProcessOperationDO::getId));
    }

    default List<VisitorProcessOperationDO> selectListByApplicationId(Long applicationId) {
        return selectList(new LambdaQueryWrapperX<VisitorProcessOperationDO>()
                .eq(VisitorProcessOperationDO::getApplicationId, applicationId)
                .orderByAsc(VisitorProcessOperationDO::getOperationTime));
    }

    default List<VisitorProcessOperationDO> selectListByProcessInstanceId(String processInstanceId) {
        return selectList(new LambdaQueryWrapperX<VisitorProcessOperationDO>()
                .eq(VisitorProcessOperationDO::getProcessInstanceId, processInstanceId)
                .orderByAsc(VisitorProcessOperationDO::getOperationTime));
    }

    default List<VisitorProcessOperationDO> selectListByTaskId(String taskId) {
        return selectList(VisitorProcessOperationDO::getTaskId, taskId);
    }

    default List<VisitorProcessOperationDO> selectListByOperationType(Integer operationType) {
        return selectList(VisitorProcessOperationDO::getOperationType, operationType);
    }

    default List<VisitorProcessOperationDO> selectListByOperatorId(Long operatorId) {
        return selectList(VisitorProcessOperationDO::getOperatorId, operatorId);
    }

    default List<VisitorProcessOperationDO> selectListByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return selectList(new LambdaQueryWrapperX<VisitorProcessOperationDO>()
                .ge(VisitorProcessOperationDO::getOperationTime, startTime)
                .le(VisitorProcessOperationDO::getOperationTime, endTime));
    }

    default List<VisitorProcessOperationDO> selectListByType(Integer operationType, LocalDateTime startTime, LocalDateTime endTime) {
        return selectList(new LambdaQueryWrapperX<VisitorProcessOperationDO>()
                .eq(VisitorProcessOperationDO::getOperationType, operationType)
                .ge(VisitorProcessOperationDO::getOperationTime, startTime)
                .le(VisitorProcessOperationDO::getOperationTime, endTime));
    }

    default Long selectCountByType(Integer operationType, LocalDateTime startTime, LocalDateTime endTime) {
        return selectCount(new LambdaQueryWrapperX<VisitorProcessOperationDO>()
                .eq(VisitorProcessOperationDO::getOperationType, operationType)
                .ge(VisitorProcessOperationDO::getOperationTime, startTime)
                .le(VisitorProcessOperationDO::getOperationTime, endTime));
    }

    default VisitorProcessOperationDO selectLatestByApplicationId(Long applicationId) {
        return selectOne(new LambdaQueryWrapperX<VisitorProcessOperationDO>()
                .eq(VisitorProcessOperationDO::getApplicationId, applicationId)
                .orderByDesc(VisitorProcessOperationDO::getOperationTime)
                .last("LIMIT 1"));
    }

    /**
     * 统计操作员记录鏁伴噺鎸夋搷浣滅被鍨嬪垎缁?     */
    List<Object[]> selectCountGroupByOperationType();

    /**
     * 统计操作员记录鏁伴噺鎸夋搷浣滅粨鏋滃垎缁?     */
    List<Object[]> selectCountGroupByOperationResult();

    /**
     * 统计操作员记录鏁伴噺鎸夋搷浣滀汉鍒嗙粍
     */
    List<Object[]> selectCountGroupByOperator();

}

