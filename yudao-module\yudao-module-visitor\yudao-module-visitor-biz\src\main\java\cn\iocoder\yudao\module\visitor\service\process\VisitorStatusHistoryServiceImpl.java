﻿package cn.iocoder.yudao.module.visitor.service.process;

import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorStatusHistoryDO;
import cn.iocoder.yudao.module.visitor.dal.mysql.VisitorStatusHistoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 访客状态佸彉鏇村巻鍙?Service 实现类? *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Service
@Validated
@Slf4j
public class VisitorStatusHistoryServiceImpl implements VisitorStatusHistoryService {

    @Resource
    private VisitorStatusHistoryMapper statusHistoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStatusHistory(VisitorStatusHistoryDO history) {
        log.info("[createStatusHistory] 创建状态佸彉鏇村巻鍙诧紝申请ID锛歿}", history.getApplicationId());
        
        statusHistoryMapper.insert(history);
        
        log.info("[createStatusHistory] 状态佸彉鏇村巻鍙插垱寤烘垚鍔燂紝ID锛歿}", history.getId());
        return history.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long recordStatusChange(Long applicationId, String processInstanceId, Integer beforeStatus, 
                                  Integer afterStatus, String changeReason, Integer changeType, 
                                  Long changePersonId, String changePersonName) {
        log.info("[recordStatusChange] 记录状态佸彉鏇达紝申请ID锛歿}锛寋}鈫抺}", applicationId, beforeStatus, afterStatus);
        
        // 鑾峰彇鍙樻洿搴忓彿
        Integer changeSequence = statusHistoryMapper.selectMaxSequenceByApplicationId(applicationId);
        changeSequence = changeSequence == null ? 1 : changeSequence + 1;
        
        VisitorStatusHistoryDO history = VisitorStatusHistoryDO.builder()
                .applicationId(applicationId)
                .processInstanceId(processInstanceId)
                .changeSequence(changeSequence)
                .beforeStatus(beforeStatus)
                .afterStatus(afterStatus)
                .changeReason(changeReason)
                .changeType(changeType)
                .changePersonId(changePersonId)
                .changePersonName(changePersonName)
                .changeTime(LocalDateTime.now())
                .build();
        
        statusHistoryMapper.insert(history);
        
        log.info("[recordStatusChange] 状态佸彉鏇磋褰曟垚鍔燂紝ID锛歿}", history.getId());
        return history.getId();
    }

    @Override
    public VisitorStatusHistoryDO getStatusHistory(Long id) {
        return statusHistoryMapper.selectById(id);
    }

    @Override
    public List<VisitorStatusHistoryDO> getHistoryByApplicationId(Long applicationId) {
        return statusHistoryMapper.selectListByApplicationId(applicationId);
    }

    @Override
    public List<VisitorStatusHistoryDO> getHistoryByProcessInstanceId(String processInstanceId) {
        return statusHistoryMapper.selectListByProcessInstanceId(processInstanceId);
    }

    @Override
    public Object getStatusChangeStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("[getStatusChangeStatistics] 鑾峰彇状态佸彉鏇寸粺璁★紝时间鑼冨洿锛歿} - {}", startTime, endTime);
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 统计鍚勭姸鎬佺殑鍙樻洿娆℃暟
        statistics.put("pendingToApproved", statusHistoryMapper.selectCountByStatusChange(1, 2, startTime, endTime));
        statistics.put("approvedToEntered", statusHistoryMapper.selectCountByStatusChange(2, 4, startTime, endTime));
        statistics.put("enteredToExited", statusHistoryMapper.selectCountByStatusChange(4, 5, startTime, endTime));
        statistics.put("rejectedCount", statusHistoryMapper.selectCountByAfterStatus(3, startTime, endTime));
        
        return statistics;
    }

    @Override
    public Object getAverageProcessingTime(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("[getAverageProcessingTime] 鑾峰彇骞冲潎澶勭悊时长统计锛屾椂闂磋寖鍥达細{} - {}", startTime, endTime);
        
        Map<String, Object> avgTimes = new HashMap<>();
        
        // 计算鍚勯樁娈靛钩鍧囧鐞嗘椂闂?        avgTimes.put("avgApprovalTime", statusHistoryMapper.selectAvgDurationByStatusChange(1, 2, startTime, endTime));
        avgTimes.put("avgEntryTime", statusHistoryMapper.selectAvgDurationByStatusChange(2, 4, startTime, endTime));
        avgTimes.put("avgVisitTime", statusHistoryMapper.selectAvgDurationByStatusChange(4, 5, startTime, endTime));
        
        return avgTimes;
    }

}

