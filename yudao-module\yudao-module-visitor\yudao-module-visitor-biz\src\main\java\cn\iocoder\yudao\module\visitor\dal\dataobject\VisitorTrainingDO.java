﻿package cn.iocoder.yudao.module.visitor.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.visitor.enums.TrainingTypeEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.util.List;

/**
 * 访客培训閰嶇疆 DO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@TableName(value = "visitor_training", autoResultMap = true)
@KeySequence("visitor_training_seq") // 鐢ㄤ簬 Oracle銆丳ostgreSQL銆並ingbase銆丏B2銆丠2 鏁版嵁搴撶殑涓婚敭鑷銆傚鏋滄槸 MySQL 绛夋暟鎹簱锛屽彲涓嶅啓
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorTrainingDO extends BaseDO {

    /**
     * 涓婚敭ID
     */
    @TableId
    private Long id;

    /**
     * 培训名称
     */
    private String trainingName;

    /**
     * 培训类型
     *
     * 鏋氫妇 {@link TrainingTypeEnum}
     */
    private Integer trainingType;

    /**
     * 培训鍐呭
     */
    private String trainingContent;

    /**
     * 培训鏉愭枡URL鏁扮粍锛歔{type:pdf/video/image,url,name,size}]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<TrainingMaterial> trainingMaterials;

    /**
     * 最小忓煿璁椂闀匡紙分钟锛?     */
    private Integer minDuration;

    /**
     * 最小澶у煿璁椂闀匡紙分钟锛?     */
    private Integer maxDuration;

    /**
     * 鏄惁必修锛?-鍚?1-鏄?     */
    private Integer isRequired;

    /**
     * 閫傜敤访客类型锛?-鏅€氳瀹?2-鏀垮簻访客 3-鏂藉伐鎵垮寘鍟?0-鍏ㄩ儴
     */
    private Integer visitorType;

    /**
     * 鏄惁鏈夎€冭瘯锛?-鍚?1-鏄?     */
    private Integer hasExam;

    /**
     * 考试棰樼洰JSON
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ExamQuestion> examQuestions;

    /**
     * 鍙婃牸分数
     */
    private Integer passScore;

    /**
     * 璇佷功妯℃澘URL
     */
    private String certificateTemplate;

    /**
     * 鎺掑簭
     */
    private Integer sortOrder;

    /**
     * 状态侊細0-禁用 1-鍚敤
     */
    private Integer status;

    /**
     * 培训鏉愭枡鍐呴儴绫?     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrainingMaterial {
        /**
         * 鏉愭枡类型锛歱df/video/image
         */
        private String type;
        /**
         * 鏉愭枡URL
         */
        private String url;
        /**
         * 鏉愭枡名称
         */
        private String name;
        /**
         * 鏉愭枡澶у皬
         */
        private Long size;
    }

    /**
     * 考试棰樼洰鍐呴儴绫?     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExamQuestion {
        /**
         * 棰樼洰ID
         */
        private String questionId;
        /**
         * 棰樼洰类型锛歴ingle-鍗曢€?multiple-澶氶€?judge-鍒ゆ柇
         */
        private String questionType;
        /**
         * 棰樼洰鍐呭
         */
        private String questionContent;
        /**
         * 閫夐」列表
         */
        private List<String> options;
        /**
         * 姝ｇ‘绛旀
         */
        private List<String> correctAnswers;
        /**
         * 棰樼洰鍒嗗€?         */
        private Integer score;
    }

}

