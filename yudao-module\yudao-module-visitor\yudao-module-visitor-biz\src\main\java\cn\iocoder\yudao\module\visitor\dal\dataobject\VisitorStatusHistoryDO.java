﻿package cn.iocoder.yudao.module.visitor.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 访客状态佸彉鏇村巻鍙?DO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@TableName(value = "visitor_status_history", autoResultMap = true)
@KeySequence("visitor_status_history_seq") // 鐢ㄤ簬 Oracle銆丳ostgreSQL銆並ingbase銆丏B2銆丠2 鏁版嵁搴撶殑涓婚敭鑷銆傚鏋滄槸 MySQL 绛夋暟鎹簱锛屽彲涓嶅啓
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorStatusHistoryDO extends BaseDO {

    /**
     * 涓婚敭ID
     */
    @TableId
    private Long id;

    /**
     * 申请鍗旾D
     */
    private Long applicationId;

    /**
     * Flowable娴佺▼瀹炰緥ID
     */
    private String processInstanceId;

    /**
     * 鍙樻洿搴忓彿
     */
    private Integer changeSequence;

    /**
     * 鍙樻洿鍓嶇姸鎬?     */
    private Integer beforeStatus;

    /**
     * 鍙樻洿鍚庣姸鎬?     */
    private Integer afterStatus;

    /**
     * 状态佸悕绉?     */
    private String statusName;

    /**
     * 鍙樻洿鍘熷洜
     */
    private String changeReason;

    /**
     * 鍙樻洿类型锛?-正常娴佽浆 2-浜哄伐骞查 3-绯荤粺鑷姩 4-异常澶勭悊 5-娴佺▼鍥為€€
     */
    private Integer changeType;

    /**
     * 鍙樻洿瑙﹀彂鏂瑰紡锛?-鐢ㄦ埛操作员 2-瀹氭椂浠诲姟 3-绯荤粺浜嬩欢 4-澶栭儴接口
     */
    private Integer changeTrigger;

    /**
     * 鍙樻洿操作员浜篒D
     */
    private Long changePersonId;

    /**
     * 鍙樻洿操作员浜哄鍚?     */
    private String changePersonName;

    /**
     * 鍙樻洿时间
     */
    private LocalDateTime changeTime;

    /**
     * 鍦ㄥ墠涓€状态佸仠鐣欐椂闀匡紙分钟锛?     */
    private Integer durationMinutes;

    /**
     * 鍏宠仈鐨勪换鍔D
     */
    private String relatedTaskId;

    /**
     * 鍏宠仈鐨勬搷浣滆褰旾D
     */
    private Long relatedOperationId;

    /**
     * 涓氬姟鏁版嵁蹇収
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> businessData;

    /**
     * 绯荤粺鍙橀噺蹇収
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> systemVariables;

    /**
     * 鏄惁宸插彂閫侀€氱煡锛?-鍚?1-鏄?     */
    private Integer notificationSent;

    /**
     * 閫氱煡鍙戦€佹椂闂?     */
    private LocalDateTime notificationTime;

}

