﻿package cn.iocoder.yudao.module.visitor.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.visitor.enums.ApprovalResultEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客娴佺▼瀹℃壒鎰忚 DO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@TableName(value = "visitor_approval_opinion", autoResultMap = true)
@KeySequence("visitor_approval_opinion_seq") // 鐢ㄤ簬 Oracle銆丳ostgreSQL銆並ingbase銆丏B2銆丠2 鏁版嵁搴撶殑涓婚敭鑷銆傚鏋滄槸 MySQL 绛夋暟鎹簱锛屽彲涓嶅啓
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorApprovalOpinionDO extends BaseDO {

    /**
     * 涓婚敭ID
     */
    @TableId
    private Long id;

    /**
     * 申请鍗旾D
     */
    private Long applicationId;

    /**
     * Flowable娴佺▼瀹炰緥ID
     */
    private String processInstanceId;

    /**
     * Flowable浠诲姟ID
     */
    private String taskId;

    /**
     * 浠诲姟瀹氫箟Key
     */
    private String taskDefinitionKey;

    /**
     * 瀹℃壒绾у埆锛?-联系人虹‘璁?2-部门涓荤瀹℃壒 3-缁肩閮ㄥ鎵?4-安全閮ㄥ鎵?     */
    private Integer approvalLevel;

    /**
     * 瀹℃壒搴忓彿锛堝悓绾у浜哄鎵规椂浣跨敤锛?     */
    private Integer approvalSequence;

    /**
     * 瀹℃壒浜篒D
     */
    private Long approverId;

    /**
     * 瀹℃壒浜哄鍚?     */
    private String approverName;

    /**
     * 瀹℃壒浜洪儴闂↖D
     */
    private Long approverDeptId;

    /**
     * 瀹℃壒浜洪儴闂ㄥ悕绉?     */
    private String approverDeptName;

    /**
     * 瀹℃壒浜鸿鑹?     */
    private String approverRole;

    /**
     * 瀹℃壒类型锛?-鍗曚汉瀹℃壒 2-浼氱 3-鎴栫 4-渚濇瀹℃壒
     */
    private Integer approvalType;

    /**
     * 瀹℃壒缁撴灉
     *
     * 鏋氫妇 {@link ApprovalResultEnum}
     */
    private Integer approvalResult;

    /**
     * 瀹℃壒时间
     */
    private LocalDateTime approvalTime;

    /**
     * 瀹℃壒鑰楁椂锛堝垎閽燂級
     */
    private Integer approvalDuration;

    /**
     * 鎰忚类型锛?-鍚屾剰 2-鏈夋潯浠跺悓鎰?3-涓嶅悓鎰?4-寤鸿淇敼
     */
    private Integer opinionType;

    /**
     * 瀹℃壒鎰忚鍐呭
     */
    private String opinionContent;

    /**
     * 鎰忚鎽樿
     */
    private String opinionSummary;

    /**
     * 闄勫姞条件鎴栬姹?     */
    private String conditionsRequirements;

    /**
     * 椋庨櫓璇勪及鎰忚
     */
    private String riskAssessment;

    /**
     * 安全寤鸿
     */
    private String securitySuggestions;

    /**
     * 瀹℃壒闄勪欢URL鏁扮粍
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachmentUrls;

    /**
     * 鐢靛瓙绛惧悕鍥剧墖URL
     */
    private String signatureImage;

    /**
     * 琚鎵樹汉ID锛堣浆浜ゆ椂浣跨敤锛?     */
    private Long delegatePersonId;

    /**
     * 濮旀墭鍘熷洜
     */
    private String delegateReason;

    /**
     * 鏄惁涓烘渶缁堝鎵癸細0-鍚?1-鏄?     */
    private Integer isFinalApproval;

    /**
     * 涓嬬骇瀹℃壒寤鸿
     */
    private String nextApproverSuggestion;

    /**
     * 瀹℃壒鏉冮噸锛堜細绛炬椂浣跨敤锛?     */
    private BigDecimal approvalWeight;

    /**
     * 瀹℃壒IP地址
     */
    private String ipAddress;

    /**
     * 瀹℃壒璁惧信息
     */
    private String deviceInfo;

}

