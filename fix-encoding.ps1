# PowerShell脚本：修复visitor模块中文编码问题

# 定义编码映射表
$encodingMap = @{
    "绠＄悊鍚庡彴" = "管理后台"
    "璁垮鐢宠" = "访客申请"
    "璁垮鍩硅" = "访客培训"
    "璁垮杩涘嚭璁板綍" = "访客进出记录"
    "璁垮娴佺▼" = "访客流程"
    "璁垮绫诲瀷" = "访客类型"
    "璁垮濮撳悕" = "访客姓名"
    "璁垮鐢佃瘽" = "访客电话"
    "璁垮鐓х墖" = "访客照片"
    "鏉ヨ鍗曚綅" = "来访单位"
    "鏉ヨ浜嬬敱" = "来访事由"
    "鑱旂郴鏂瑰紡" = "联系方式"
    "鑱旂郴浜虹數璇?" = "联系人电话"
    "鍘傚唴鑱旂郴浜?" = "厂内联系人"
    "韬唤璇佸彿鐮?" = "身份证号码"
    "璁块棶鐩殑" = "访问目的"
    "鍒拌鍘傚尯" = "到访厂区"
    "鍒拌鏂瑰紡" = "到访方式"
    "棰勮鍒拌寮€濮嬫椂闂?" = "预计到访开始时间"
    "棰勮鍒拌缁撴潫鏃堕棿" = "预计到访结束时间"
    "鏄惁椹捐溅" = "是否驾车"
    "杞︾墝鍙?" = "车牌号"
    "杞﹁締绫诲瀷" = "车辆类型"
    "琛岄┒璇佺紪鍙?" = "行驶证编号"
    "杞﹁締鐓х墖" = "车辆照片"
    "鍚岃浜烘暟閲?" = "同行人数量"
    "鍚岃浜轰俊鎭?" = "同行人信息"
    "鏄惁闇€瑕佷綇瀹?" = "是否需要住宿"
    "鏄惁鍓嶅線楗爞灏遍" = "是否前往饭堂就餐"
    "绱ф€ヨ仈绯讳汉" = "紧急联系人"
    "绱ф€ヨ仈绯荤數璇?" = "紧急联系电话"
    "鐗规畩瑕佹眰" = "特殊要求"
    "鐢宠ID" = "申请ID"
    "鏄惁閫氳繃" = "是否通过"
    "瀹℃壒缁撴灉" = "审批结果"
    "瀹℃壒鎰忚" = "审批意见"
    "鏄惁纭" = "是否确认"
    "纭缁撴灉" = "确认结果"
    "纭鎰忚" = "确认意见"
    "涓嶈兘涓虹┖" = "不能为空"
    "缂栧彿" = "编号"
    "鍒涘缓" = "创建"
    "鏇存柊" = "更新"
    "鍒犻櫎" = "删除"
    "鑾峰緱" = "获得"
    "鍒嗛〉" = "分页"
    "鍒楄〃" = "列表"
    "瀵煎嚭" = "导出"
    "鏌ヨ" = "查询"
    "鏉′欢" = "条件"
    "鐘舵€?" = "状态"
    "鍒涘缓鏃堕棿" = "创建时间"
    "鎿嶄綔绫诲瀷" = "操作类型"
    "鎿嶄綔鏃堕棿" = "操作时间"
    "鎿嶄綔鍛業D" = "操作员ID"
    "闂ㄥ矖浣嶇疆" = "门岗位置"
    "鐜板満璁垮鐓х墖" = "现场访客照片"
    "鎿嶄綔鍛樺鍚?" = "操作员姓名"
    "鐢宠鍗旾D" = "申请单ID"
    "鍩硅ID" = "培训ID"
    "鍩硅鍚嶇О" = "培训名称"
    "鍩硅绫诲瀷" = "培训类型"
    "閫傜敤璁垮绫诲瀷" = "适用访客类型"
    "瀹夊叏鍩硅" = "安全培训"
    "鎶€鏈儴" = "技术部"
    "涓滈棬" = "东门"
    "浜珹12345" = "京A12345"
    "鏌愭煇鍏徃" = "某某公司"
    "寮犱笁" = "张三"
    "鏉庡洓" = "李四"
    "鐜嬩簲" = "王五"
    "鍟嗗姟娲借皥" = "商务洽谈"
    "绠＄悊鍛?" = "管理员"
    "鍚屾剰璁块棶" = "同意访问"
    "娉ㄦ剰瀹夊叏" = "注意安全"
    "鍚屾剰鎺ュ緟璇ヨ瀹?" = "同意接待该访客"
    "闇€瑕佽疆妞呴€氶亾" = "需要轮椅通道"
    "A鍖?" = "A区"
    "鏁版嵁" = "数据"
    "褰撳墠鍦ㄥ洯璁垮鏁伴噺" = "当前在园访客数量"
    "鍦ㄥ洯璁垮鏁伴噺" = "在园访客数量"
    "璁垮杩涘嚭缁熻" = "访客进出统计"
    "璁垮鍏ュ洯鐧昏" = "访客入园登记"
    "璁垮鍑哄洯鐧昏" = "访客出园登记"
    "璁垮鍏ュ洯璁板綍" = "访客入园记录"
    "璁垮鍑哄洯璁板綍" = "访客出园记录"
    "璁垮鐘舵€佸彉鏇村巻鍙?" = "访客状态变更历史"
    "璁垮娴佺▼鎿嶄綔璁板綍" = "访客流程操作记录"
    "璁垮娴佺▼璺熻繘璁板綍" = "访客流程跟进记录"
    "璁垮娴佺▼寮傚父澶勭悊璁板綍" = "访客流程异常处理记录"
    "璁垮鍩硅瀹屾垚璁板綍" = "访客培训完成记录"
    "璁垮鍩硅閰嶇疆" = "访客培训配置"
    "鍒涘缓璁垮鐢宠" = "创建访客申请"
    "鏇存柊璁垮鐢宠" = "更新访客申请"
    "鍒犻櫎璁垮鐢宠" = "删除访客申请"
    "鑾峰緱璁垮鐢宠" = "获得访客申请"
    "鑾峰緱璁垮鐢宠鍒嗛〉" = "获得访客申请分页"
    "瀵煎嚭璁垮鐢宠" = "导出访客申请"
    "璁垮鐢宠瀹℃壒" = "访客申请审批"
    "璁垮鐢宠鑱旂郴浜虹'璁?" = "访客申请联系人确认"
    "鍒涘缓璁垮杩涘嚭璁板綍" = "创建访客进出记录"
    "鏇存柊璁垮杩涘嚭璁板綍" = "更新访客进出记录"
    "鍒犻櫎璁垮杩涘嚭璁板綍" = "删除访客进出记录"
    "鑾峰緱璁垮杩涘嚭璁板綍" = "获得访客进出记录"
    "鑾峰緱璁垮杩涘嚭璁板綍鍒嗛〉" = "获得访客进出记录分页"
    "瀵煎嚭璁垮杩涘嚭璁板綍" = "导出访客进出记录"
    "鑾峰彇璁垮杩涘嚭缁熻" = "获取访客进出统计"
    "鑾峰彇褰撳墠鍦ㄥ洯璁垮鏁伴噺" = "获取当前在园访客数量"
    "鍒涘缓璁垮鍩硅" = "创建访客培训"
    "鏇存柊璁垮鍩硅" = "更新访客培训"
    "鍒犻櫎璁垮鍩硅" = "删除访客培训"
    "鑾峰緱璁垮鍩硅" = "获得访客培训"
    "鑾峰緱璁垮鍩硅鍒嗛〉" = "获得访客培训分页"
    "瀵煎嚭璁垮鍩硅" = "导出访客培训"
    "鏍规嵁璁垮绫诲瀷鑾峰彇鍩硅鍒楄〃" = "根据访客类型获取培训列表"
    "鍒涘缓璁垮鍩硅瀹屾垚璁板綍" = "创建访客培训完成记录"
    "鏇存柊璁垮鍩硅瀹屾垚璁板綍" = "更新访客培训完成记录"
    "鍒犻櫎璁垮鍩硅瀹屾垚璁板綍" = "删除访客培训完成记录"
    "鑾峰緱璁垮鍩硅瀹屾垚璁板綍" = "获得访客培训完成记录"
    "鑾峰緱璁垮鍩硅瀹屾垚璁板綍鍒嗛〉" = "获得访客培训完成记录分页"
    "瀵煎嚭璁垮鍩硅瀹屾垚璁板綍" = "导出访客培训完成记录"
}

# 获取所有Java文件
$javaFiles = Get-ChildItem -Path "yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java" -Filter "*.java" -Recurse

Write-Host "找到 $($javaFiles.Count) 个Java文件需要处理"

foreach ($file in $javaFiles) {
    Write-Host "处理文件: $($file.FullName)"
    
    # 读取文件内容
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    
    # 检查是否包含乱码
    $hasEncoding = $false
    foreach ($key in $encodingMap.Keys) {
        if ($content -match [regex]::Escape($key)) {
            $hasEncoding = $true
            break
        }
    }
    
    if ($hasEncoding) {
        Write-Host "  发现编码问题，开始修复..."
        
        # 替换所有乱码
        foreach ($key in $encodingMap.Keys) {
            $value = $encodingMap[$key]
            $content = $content -replace [regex]::Escape($key), $value
        }
        
        # 写回文件
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  修复完成"
    } else {
        Write-Host "  无需修复"
    }
}

Write-Host "所有文件处理完成！"