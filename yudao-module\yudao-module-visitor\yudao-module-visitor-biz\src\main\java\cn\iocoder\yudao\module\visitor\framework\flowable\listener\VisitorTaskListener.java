﻿package cn.iocoder.yudao.module.visitor.framework.flowable.listener;

import cn.iocoder.yudao.module.visitor.framework.notification.VisitorNotificationService;
import cn.iocoder.yudao.module.visitor.service.application.VisitorApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 访客申请浠诲姟鐩戝惉鍣? *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Component("visitorTaskListener")
@Slf4j
public class VisitorTaskListener implements TaskListener {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @Resource
    private VisitorNotificationService visitorNotificationService;

    @Override
    public void notify(DelegateTask delegateTask) {
        log.info("[notify] 访客申请浠诲姟浜嬩欢锛屼换鍔D锛歿}锛屼簨浠剁被鍨嬶細{}", 
                delegateTask.getId(), delegateTask.getEventName());
        
        try {
            String eventName = delegateTask.getEventName();
            Object applicationIdObj = delegateTask.getVariable("applicationId");
            
            if (applicationIdObj != null) {
                Long applicationId = Long.valueOf(applicationIdObj.toString());
                
                switch (eventName) {
                    case "create":
                        handleTaskCreate(delegateTask, applicationId);
                        break;
                    case "assignment":
                        handleTaskAssignment(delegateTask, applicationId);
                        break;
                    case "complete":
                        handleTaskComplete(delegateTask, applicationId);
                        break;
                    default:
                        log.info("[notify] 鏈鐞嗙殑浠诲姟浜嬩欢锛歿}", eventName);
                        break;
                }
            }
            
            log.info("[notify] 访客申请浠诲姟鐩戝惉鍣ㄦ墽琛屽畬鎴?);

        } catch (Exception e) {
            log.error("[notify] 访客申请浠诲姟鐩戝惉鍣ㄦ墽琛屽け璐?, e);
            throw new RuntimeException("浠诲姟鐩戝惉鍣ㄦ墽琛屽け璐?, e);
        }
    }

    /**
     * 澶勭悊浠诲姟创建浜嬩欢
     */
    private void handleTaskCreate(DelegateTask task, Long applicationId) {
        log.info("[handleTaskCreate] 浠诲姟创建锛屼换鍔″悕绉帮細{}锛岀敵璇稩D锛歿}", task.getName(), applicationId);

        try {
            // 记录浠诲姟创建璇︾粏信息
            String taskInfo = String.format("浠诲姟ID锛?s锛屼换鍔″悕绉帮細%s锛屼换鍔″畾涔塊ey锛?s锛屾祦绋嬪疄渚婭D锛?s",
                    task.getId(), task.getName(), task.getTaskDefinitionKey(), task.getProcessInstanceId());
            log.info("[handleTaskCreate] 浠诲姟创建璇︽儏锛歿}", taskInfo);

            // 更新申请状态佷负瀹℃壒涓紙濡傛灉闇€瑕侊級
            if (applicationId != null) {
                visitorApplicationService.updateVisitorApplicationStatus(applicationId,
                        cn.iocoder.yudao.module.visitor.enums.VisitorStatusEnum.PENDING_APPROVAL.getStatus(),
                        "浠诲姟宸插垱寤猴細" + task.getName());
            }
        } catch (Exception e) {
            log.error("[handleTaskCreate] 澶勭悊浠诲姟创建浜嬩欢失败", e);
        }
    }

    /**
     * 澶勭悊浠诲姟鍒嗛厤浜嬩欢
     */
    private void handleTaskAssignment(DelegateTask task, Long applicationId) {
        log.info("[handleTaskAssignment] 浠诲姟鍒嗛厤锛屽垎閰嶇粰锛歿}锛岀敵璇稩D锛歿}", task.getAssignee(), applicationId);

        try {
            // 鍙戦€佷换鍔″垎閰嶉€氱煡缁欒鍒嗛厤鐨勭敤鎴?            if (task.getAssignee() != null && applicationId != null) {
                // 鑾峰彇申请信息
                cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO application =
                        visitorApplicationService.getVisitorApplication(applicationId);

                if (application != null) {
                    // 鍙戦€佷换鍔″垎閰嶉€氱煡
                    visitorNotificationService.sendTaskAssignmentNotification(
                            Long.valueOf(task.getAssignee()), task.getName(), application);

                    log.info("[handleTaskAssignment] 浠诲姟鍒嗛厤閫氱煡宸插彂閫侊紝浠诲姟锛歿}锛屽垎閰嶇粰锛歿}",
                            task.getName(), task.getAssignee());
                }
            }
        } catch (Exception e) {
            log.error("[handleTaskAssignment] 澶勭悊浠诲姟鍒嗛厤浜嬩欢失败", e);
        }
    }

    /**
     * 澶勭悊浠诲姟完成浜嬩欢
     */
    private void handleTaskComplete(DelegateTask task, Long applicationId) {
        log.info("[handleTaskComplete] 浠诲姟完成锛屼换鍔″悕绉帮細{}锛岀敵璇稩D锛歿}", task.getName(), applicationId);

        try {
            // 记录浠诲姟完成鐨勮缁嗕俊鎭拰瀹℃壒鎰忚
            String completionInfo = String.format("浠诲姟ID锛?s锛屼换鍔″悕绉帮細%s锛屽畬鎴愪汉锛?s锛屽畬鎴愭椂闂达細%s",
                    task.getId(), task.getName(), task.getAssignee(), java.time.LocalDateTime.now());
            log.info("[handleTaskComplete] 浠诲姟完成璇︽儏锛歿}", completionInfo);

            // 鑾峰彇瀹℃壒鎰忚锛堝鏋滄湁锛?            Object approvalOpinion = task.getVariable("approvalOpinion");
            Object approvalResult = task.getVariable("approvalResult");

            if (approvalOpinion != null || approvalResult != null) {
                log.info("[handleTaskComplete] 瀹℃壒鎰忚锛歿}锛屽鎵圭粨鏋滐細{}", approvalOpinion, approvalResult);
            }

            // 鍙戦€佷换鍔″畬鎴愰€氱煡缁欑敵璇蜂汉
            if (applicationId != null) {
                cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO application =
                        visitorApplicationService.getVisitorApplication(applicationId);

                if (application != null) {
                    visitorNotificationService.sendTaskCompletionNotification(task.getName(), application);
                }
            }
        } catch (Exception e) {
            log.error("[handleTaskComplete] 澶勭悊浠诲姟完成浜嬩欢失败", e);
        }
    }

}

