﻿package cn.iocoder.yudao.module.visitor.controller.admin.application.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理后台 - 访客申请创建 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客申请创建 Request VO")
@Data
public class VisitorApplicationCreateReqVO {

    @Schema(description = "访客类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "访客类型不能为空")
    private Integer visitorType;

    @Schema(description = "来访单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "某某公司")
    @NotBlank(message = "来访单位不能为空")
    private String companyName;

    @Schema(description = "访客姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotBlank(message = "访客姓名不能为空")
    private String visitorName;

    @Schema(description = "联系方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
    @NotBlank(message = "联系方式不能为空")
    private String visitorPhone;

    @Schema(description = "身份证号码", example = "110101199001011234")
    private String idCard;

    @Schema(description = "访客照片URL", example = "https://example.com/photo.jpg")
    private String visitorPhoto;

    @Schema(description = "来访事由", requiredMode = Schema.RequiredMode.REQUIRED, example = "商务洽谈")
    @NotBlank(message = "来访事由不能为空")
    private String visitReason;

    @Schema(description = "访问目的", example = "2")
    private Integer visitPurpose;

    @Schema(description = "厂内联系人", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotBlank(message = "厂内联系人不能为空")
    private String contactPerson;

    @Schema(description = "联系人电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "13900139000")
    @NotBlank(message = "联系人电话不能为空")
    private String contactPhone;

    @Schema(description = "联系人部门ID", example = "1")
    private Long contactDeptId;

    @Schema(description = "联系人部门名称", example = "技术部")
    private String contactDeptName;

    @Schema(description = "预计到访开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预计到访开始时间不能为空")
    private LocalDateTime visitStartTime;

    @Schema(description = "预计到访结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预计到访结束时间不能为空")
    private LocalDateTime visitEndTime;

    @Schema(description = "到访厂区", requiredMode = Schema.RequiredMode.REQUIRED, example = "A区")
    @NotBlank(message = "到访厂区不能为空")
    private String visitArea;

    @Schema(description = "到访方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "到访方式不能为空")
    private Integer visitMethod;

    @Schema(description = "是否驾车", example = "0")
    private Integer hasVehicle;

    @Schema(description = "车牌号", example = "京A12345")
    private String vehiclePlate;

    @Schema(description = "车辆类型", example = "1")
    private Integer vehicleType;

    @Schema(description = "行驶证编号", example = "123456789")
    private String vehicleLicense;

    @Schema(description = "车辆照片URL", example = "https://example.com/vehicle.jpg")
    private String vehiclePhoto;

    @Schema(description = "同行人数量", example = "2")
    private Integer companionCount;

    @Schema(description = "同行人信息")
    private List<CompanionInfo> companionInfo;

    @Schema(description = "是否需要住宿", example = "0")
    private Integer needAccommodation;

    @Schema(description = "是否前往饭堂就餐", example = "0")
    private Integer needDining;

    @Schema(description = "紧急联系人", example = "王五")
    private String emergencyContact;

    @Schema(description = "紧急联系电话", example = "13700137000")
    private String emergencyPhone;

    @Schema(description = "特殊要求", example = "需要轮椅通道")
    private String specialRequirements;

    @Schema(description = "同行人信息")
    @Data
    public static class CompanionInfo {
        @Schema(description = "姓名", example = "张三")
        private String name;
        
        @Schema(description = "电话", example = "13800138000")
        private String phone;
        
        @Schema(description = "身份证号", example = "110101199001011234")
        private String idCard;
        
        @Schema(description = "照片URL", example = "https://example.com/photo.jpg")
        private String photo;
    }

}
