﻿package cn.iocoder.yudao.module.visitor.controller.admin.application.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 管理后台 - 访客出园登记 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客出园登记 Request VO")
@Data
public class VisitorApplicationExitReqVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "申请ID不能为空")
    private Long id;

    @Schema(description = "操作员業D", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "操作员業D不能为空")
    private Long operatorId;

    @Schema(description = "门岗浣嶇疆", requiredMode = Schema.RequiredMode.REQUIRED, example = "涓滈棬")
    @NotNull(message = "门岗浣嶇疆不能为空")
    private String gateLocation;

    @Schema(description = "备注", example = "正常出园")
    private String remarks;

}

