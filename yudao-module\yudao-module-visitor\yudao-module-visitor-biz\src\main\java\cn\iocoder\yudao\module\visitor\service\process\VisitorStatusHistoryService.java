﻿package cn.iocoder.yudao.module.visitor.service.process;

import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorStatusHistoryDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客状态佸彉鏇村巻鍙?Service 接口
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
public interface VisitorStatusHistoryService {

    /**
     * 创建状态佸彉鏇村巻鍙?     *
     * @param history 状态佸彉鏇村巻鍙?     * @return 编号
     */
    Long createStatusHistory(VisitorStatusHistoryDO history);

    /**
     * 记录状态佸彉鏇?     *
     * @param applicationId 申请ID
     * @param processInstanceId 娴佺▼瀹炰緥ID
     * @param beforeStatus 鍙樻洿鍓嶇姸鎬?     * @param afterStatus 鍙樻洿鍚庣姸鎬?     * @param changeReason 鍙樻洿鍘熷洜
     * @param changeType 鍙樻洿类型
     * @param changePersonId 鍙樻洿浜篒D
     * @param changePersonName 鍙樻洿浜哄鍚?     * @return 鍘嗗彶记录ID
     */
    Long recordStatusChange(Long applicationId, String processInstanceId, Integer beforeStatus, 
                           Integer afterStatus, String changeReason, Integer changeType, 
                           Long changePersonId, String changePersonName);

    /**
     * 获得状态佸彉鏇村巻鍙?     *
     * @param id 编号
     * @return 状态佸彉鏇村巻鍙?     */
    VisitorStatusHistoryDO getStatusHistory(Long id);

    /**
     * 鏍规嵁申请ID鑾峰彇状态佸彉鏇村巻鍙插垪琛?     *
     * @param applicationId 申请ID
     * @return 状态佸彉鏇村巻鍙插垪琛?     */
    List<VisitorStatusHistoryDO> getHistoryByApplicationId(Long applicationId);

    /**
     * 鏍规嵁娴佺▼瀹炰緥ID鑾峰彇状态佸彉鏇村巻鍙插垪琛?     *
     * @param processInstanceId 娴佺▼瀹炰緥ID
     * @return 状态佸彉鏇村巻鍙插垪琛?     */
    List<VisitorStatusHistoryDO> getHistoryByProcessInstanceId(String processInstanceId);

    /**
     * 鑾峰彇状态佸彉鏇寸粺璁?     *
     * @param startTime 开始嬫椂闂?     * @param endTime 缁撴潫时间
     * @return 统计缁撴灉
     */
    Object getStatusChangeStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 鑾峰彇骞冲潎澶勭悊时长统计
     *
     * @param startTime 开始嬫椂闂?     * @param endTime 缁撴潫时间
     * @return 骞冲潎澶勭悊时长锛堝垎閽燂級
     */
    Object getAverageProcessingTime(LocalDateTime startTime, LocalDateTime endTime);

}

