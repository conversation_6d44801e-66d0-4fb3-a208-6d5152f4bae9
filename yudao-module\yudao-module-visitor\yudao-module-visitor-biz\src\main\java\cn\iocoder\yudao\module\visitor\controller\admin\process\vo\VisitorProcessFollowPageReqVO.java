﻿package cn.iocoder.yudao.module.visitor.controller.admin.process.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 访客娴佺▼璺熻繘记录分页 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客娴佺▼璺熻繘记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitorProcessFollowPageReqVO extends PageParam {

    @Schema(description = "申请鍗旾D", example = "1024")
    private Long applicationId;

    @Schema(description = "Flowable娴佺▼瀹炰緥ID", example = "proc_inst_123")
    private String processInstanceId;

    @Schema(description = "璺熻繘类型", example = "1")
    private Integer followType;

    @Schema(description = "璺熻繘浜篒D", example = "1")
    private Long followPersonId;

    @Schema(description = "鐩爣瀵硅薄ID", example = "2")
    private Long targetPersonId;

    @Schema(description = "绱ф€ョ▼搴?, example = "2")
    private Integer urgencyLevel;

    @Schema(description = "鏄惁宸茶В鍐?, example = "0")
    private Integer isResolved;

    @Schema(description = "璺熻繘时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] followTime;

}

