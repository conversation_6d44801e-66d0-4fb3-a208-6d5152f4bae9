﻿package cn.iocoder.yudao.module.visitor.framework.notification;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.system.api.sms.SmsSendApi;
import cn.iocoder.yudao.module.system.api.sms.dto.send.SmsSendSingleToUserReqDTO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 访客閫氱煡鏈嶅姟
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Service
@Slf4j
public class VisitorNotificationService {

    @Resource
    private SmsSendApi smsSendApi;

    // 鐭俊妯℃澘缂栫爜
    private static final String SMS_TEMPLATE_APPLICATION_SUBMITTED = "VISITOR_APPLICATION_SUBMITTED";
    private static final String SMS_TEMPLATE_APPLICATION_APPROVED = "VISITOR_APPLICATION_APPROVED";
    private static final String SMS_TEMPLATE_APPLICATION_REJECTED = "VISITOR_APPLICATION_REJECTED";
    private static final String SMS_TEMPLATE_QR_CODE_GENERATED = "VISITOR_QR_CODE_GENERATED";
    private static final String SMS_TEMPLATE_CONTACT_CONFIRM = "VISITOR_CONTACT_CONFIRM";

    /**
     * 鍙戦€佺敵璇锋彁浜ら€氱煡
     *
     * @param application 访客申请
     */
    public void sendApplicationSubmittedNotification(VisitorApplicationDO application) {
        try {
            log.info("[sendApplicationSubmittedNotification] 鍙戦€佺敵璇锋彁浜ら€氱煡锛岀敵璇稩D锛歿}", application.getId());

            // 鍙戦€佺煭淇＄粰访客
            Map<String, Object> visitorParams = new HashMap<>();
            visitorParams.put("visitorName", application.getVisitorName());
            visitorParams.put("applicationNo", application.getApplicationNo());
            visitorParams.put("companyName", application.getCompanyName());

            sendSmsToVisitor(application.getVisitorPhone(), SMS_TEMPLATE_APPLICATION_SUBMITTED, visitorParams);

            // 鍙戦€侀€氱煡缁欒仈绯讳汉锛堣繖閲屽彲浠ラ泦鎴愪紒涓氬井淇＄瓑锛?            sendContactConfirmNotification(application);

            log.info("[sendApplicationSubmittedNotification] 申请鎻愪氦閫氱煡鍙戦€佹垚鍔燂紝申请ID锛歿}", application.getId());

        } catch (Exception e) {
            log.error("[sendApplicationSubmittedNotification] 鍙戦€佺敵璇锋彁浜ら€氱煡失败锛岀敵璇稩D锛歿}", application.getId(), e);
        }
    }

    /**
     * 鍙戦€佽仈绯讳汉纭閫氱煡
     *
     * @param application 访客申请
     */
    public void sendContactConfirmNotification(VisitorApplicationDO application) {
        try {
            log.info("[sendContactConfirmNotification] 鍙戦€佽仈绯讳汉纭閫氱煡锛岀敵璇稩D锛歿}", application.getId());

            // 鍙戦€佺煭淇＄粰联系人?            Map<String, Object> contactParams = new HashMap<>();
            contactParams.put("contactPerson", application.getContactPerson());
            contactParams.put("visitorName", application.getVisitorName());
            contactParams.put("companyName", application.getCompanyName());
            contactParams.put("visitReason", application.getVisitReason());
            contactParams.put("applicationNo", application.getApplicationNo());

            sendSmsToContact(application.getContactPhone(), SMS_TEMPLATE_CONTACT_CONFIRM, contactParams);

            // 鍙戦€佷紒涓氬井淇￠€氱煡锛堝鏋滈厤缃簡浼佷笟寰俊锛?            sendWeChatNotificationToContact(application);

            log.info("[sendContactConfirmNotification] 联系人虹‘璁ら€氱煡鍙戦€佹垚鍔燂紝申请ID锛歿}", application.getId());

        } catch (Exception e) {
            log.error("[sendContactConfirmNotification] 鍙戦€佽仈绯讳汉纭閫氱煡失败锛岀敵璇稩D锛歿}", application.getId(), e);
        }
    }

    /**
     * 鍙戦€佸鎵归€氳繃閫氱煡
     *
     * @param application 访客申请
     */
    public void sendApplicationApprovedNotification(VisitorApplicationDO application) {
        try {
            log.info("[sendApplicationApprovedNotification] 鍙戦€佸鎵归€氳繃閫氱煡锛岀敵璇稩D锛歿}", application.getId());

            Map<String, Object> params = new HashMap<>();
            params.put("visitorName", application.getVisitorName());
            params.put("applicationNo", application.getApplicationNo());
            params.put("visitStartTime", application.getVisitStartTime().toString());
            params.put("visitArea", application.getVisitArea());
            params.put("contactPerson", application.getContactPerson());
            params.put("contactPhone", application.getContactPhone());

            sendSmsToVisitor(application.getVisitorPhone(), SMS_TEMPLATE_APPLICATION_APPROVED, params);

            log.info("[sendApplicationApprovedNotification] 瀹℃壒通过閫氱煡鍙戦€佹垚鍔燂紝申请ID锛歿}", application.getId());

        } catch (Exception e) {
            log.error("[sendApplicationApprovedNotification] 鍙戦€佸鎵归€氳繃閫氱煡失败锛岀敵璇稩D锛歿}", application.getId(), e);
        }
    }

    /**
     * 鍙戦€佸鎵归┏鍥為€氱煡
     *
     * @param application 访客申请
     * @param rejectReason 椹冲洖鍘熷洜
     */
    public void sendApplicationRejectedNotification(VisitorApplicationDO application, String rejectReason) {
        try {
            log.info("[sendApplicationRejectedNotification] 鍙戦€佸鎵归┏鍥為€氱煡锛岀敵璇稩D锛歿}", application.getId());

            Map<String, Object> params = new HashMap<>();
            params.put("visitorName", application.getVisitorName());
            params.put("applicationNo", application.getApplicationNo());
            params.put("rejectReason", StrUtil.isNotBlank(rejectReason) ? rejectReason : "涓嶇鍚堣闂姹?);

            sendSmsToVisitor(application.getVisitorPhone(), SMS_TEMPLATE_APPLICATION_REJECTED, params);

            log.info("[sendApplicationRejectedNotification] 瀹℃壒椹冲洖閫氱煡鍙戦€佹垚鍔燂紝申请ID锛歿}", application.getId());

        } catch (Exception e) {
            log.error("[sendApplicationRejectedNotification] 鍙戦€佸鎵归┏鍥為€氱煡失败锛岀敵璇稩D锛歿}", application.getId(), e);
        }
    }

    /**
     * 鍙戦€佷簩缁寸爜鐢熸垚閫氱煡
     *
     * @param application 访客申请
     * @param qrCodeUrl 二维码乁RL
     */
    public void sendQrCodeGeneratedNotification(VisitorApplicationDO application, String qrCodeUrl) {
        try {
            log.info("[sendQrCodeGeneratedNotification] 鍙戦€佷簩缁寸爜鐢熸垚閫氱煡锛岀敵璇稩D锛歿}", application.getId());

            Map<String, Object> params = new HashMap<>();
            params.put("visitorName", application.getVisitorName());
            params.put("applicationNo", application.getApplicationNo());
            params.put("visitStartTime", application.getVisitStartTime().toString());
            params.put("visitArea", application.getVisitArea());
            params.put("qrCodeUrl", qrCodeUrl);

            sendSmsToVisitor(application.getVisitorPhone(), SMS_TEMPLATE_QR_CODE_GENERATED, params);

            // 鎶勯€侀棬宀楄鍗?            sendGuardNotification(application);

            log.info("[sendQrCodeGeneratedNotification] 二维码佺敓鎴愰€氱煡鍙戦€佹垚鍔燂紝申请ID锛歿}", application.getId());

        } catch (Exception e) {
            log.error("[sendQrCodeGeneratedNotification] 鍙戦€佷簩缁寸爜鐢熸垚閫氱煡失败锛岀敵璇稩D锛歿}", application.getId(), e);
        }
    }

    /**
     * 鍙戦€佺煭淇＄粰访客
     */
    private void sendSmsToVisitor(String phone, String templateCode, Map<String, Object> params) {
        if (StrUtil.isBlank(phone)) {
            log.warn("[sendSmsToVisitor] 访客鎵嬫満鍙蜂负绌猴紝璺宠繃鐭俊鍙戦€?);
            return;
        }

        try {
            SmsSendSingleToUserReqDTO reqDTO = new SmsSendSingleToUserReqDTO();
            reqDTO.setMobile(phone);
            reqDTO.setTemplateCode(templateCode);
            reqDTO.setTemplateParams(params);

            smsSendApi.sendSingleSmsToAdmin(reqDTO);
            log.info("[sendSmsToVisitor] 鐭俊鍙戦€佹垚鍔燂紝鎵嬫満鍙凤細{}锛屾ā鏉匡細{}", phone, templateCode);

        } catch (Exception e) {
            log.error("[sendSmsToVisitor] 鐭俊鍙戦€佸け璐ワ紝鎵嬫満鍙凤細{}锛屾ā鏉匡細{}", phone, templateCode, e);
        }
    }

    /**
     * 鍙戦€佺煭淇＄粰联系人?     */
    private void sendSmsToContact(String phone, String templateCode, Map<String, Object> params) {
        if (StrUtil.isBlank(phone)) {
            log.warn("[sendSmsToContact] 联系人烘墜鏈哄彿涓虹┖锛岃烦杩囩煭淇″彂閫?);
            return;
        }

        try {
            SmsSendSingleToUserReqDTO reqDTO = new SmsSendSingleToUserReqDTO();
            reqDTO.setMobile(phone);
            reqDTO.setTemplateCode(templateCode);
            reqDTO.setTemplateParams(params);

            smsSendApi.sendSingleSmsToAdmin(reqDTO);
            log.info("[sendSmsToContact] 鐭俊鍙戦€佹垚鍔燂紝鎵嬫満鍙凤細{}锛屾ā鏉匡細{}", phone, templateCode);

        } catch (Exception e) {
            log.error("[sendSmsToContact] 鐭俊鍙戦€佸け璐ワ紝鎵嬫満鍙凤細{}锛屾ā鏉匡細{}", phone, templateCode, e);
        }
    }

    /**
     * 鍙戦€佷紒涓氬井淇￠€氱煡缁欒仈绯讳汉
     */
    private void sendWeChatNotificationToContact(VisitorApplicationDO application) {
        try {
            // 浼佷笟寰俊閫氱煡实现类
            // 娉ㄦ剰锛氳繖閲岄渶瑕佹牴鎹疄闄呯殑浼佷笟寰俊閰嶇疆鏉ュ疄鐜?            // 鐩墠鍏堣褰曟棩蹇楋紝鍚庣画鍙互闆嗘垚浼佷笟寰俊API

            String message = String.format("銆愯瀹㈢敵璇烽€氱煡銆慭n" +
                    "申请鍗曞彿锛?s\n" +
                    "访客姓名锛?s\n" +
                    "鏉ヨ浜嬬敱锛?s\n" +
                    "璇峰強鏃剁‘璁よ瀹㈢敵璇?,
                    application.getApplicationNo(),
                    application.getVisitorName(),
                    application.getVisitReason());

            log.info("[sendWeChatNotificationToContact] 浼佷笟寰俊閫氱煡鍐呭锛歿}", message);

            // TODO: 瀹為檯鐨勪紒涓氬井淇PI璋冪敤
            // weChatApi.sendMessage(application.getContactPerson(), message);

        } catch (Exception e) {
            log.error("[sendWeChatNotificationToContact] 鍙戦€佷紒涓氬井淇￠€氱煡失败锛岀敵璇稩D锛歿}", application.getId(), e);
        }
    }

    /**
     * 鍙戦€侀€氱煡缁欓棬宀楄鍗?     */
    private void sendGuardNotification(VisitorApplicationDO application) {
        try {
            // 门岗璀﹀崼閫氱煡实现类
            String guardMessage = String.format("銆愯瀹㈤€氳閫氱煡銆慭n" +
                    "申请鍗曞彿锛?s\n" +
                    "访客姓名锛?s\n" +
                    "联系人电话锛?s\n" +
                    "鏉ヨ时间锛?s\n" +
                    "车牌鍙风爜锛?s\n" +
                    "璇峰仛濂芥帴寰呭噯澶?,
                    application.getApplicationNo(),
                    application.getVisitorName(),
                    application.getVisitorPhone(),
                    application.getVisitStartTime(),
                    application.getVehiclePlate() != null ? application.getVehiclePlate() : "鏃?);

            log.info("[sendGuardNotification] 门岗璀﹀崼閫氱煡鍐呭锛歿}", guardMessage);

            // TODO: 瀹為檯鐨勯棬宀楃郴缁烝PI璋冪敤
            // guardSystemApi.sendNotification(guardMessage);

        } catch (Exception e) {
            log.error("[sendGuardNotification] 鍙戦€侀棬宀楄鍗€氱煡失败锛岀敵璇稩D锛歿}", application.getId(), e);
        }
    }

    /**
     * 鍙戦€佽繃鏈熸彁閱掗€氱煡
     *
     * @param application 访客申请
     */
    public void sendExpirationReminderNotification(VisitorApplicationDO application) {
        try {
            log.info("[sendExpirationReminderNotification] 鍙戦€佽繃鏈熸彁閱掗€氱煡锛岀敵璇稩D锛歿}", application.getId());

            Map<String, Object> params = new HashMap<>();
            params.put("visitorName", application.getVisitorName());
            params.put("applicationNo", application.getApplicationNo());
            params.put("visitEndTime", application.getVisitEndTime().toString());

            // 鍙戦€佹彁閱掔煭淇?            sendSmsToVisitor(application.getVisitorPhone(), "VISITOR_EXPIRATION_REMINDER", params);

            log.info("[sendExpirationReminderNotification] 杩囨湡鎻愰啋閫氱煡鍙戦€佹垚鍔燂紝申请ID锛歿}", application.getId());

        } catch (Exception e) {
            log.error("[sendExpirationReminderNotification] 鍙戦€佽繃鏈熸彁閱掗€氱煡失败锛岀敵璇稩D锛歿}", application.getId(), e);
        }
    }

    /**
     * 鍙戦€佷换鍔″垎閰嶉€氱煡
     */
    public void sendTaskAssignmentNotification(Long assigneeId, String taskName, VisitorApplicationDO application) {
        try {
            log.info("[sendTaskAssignmentNotification] 鍙戦€佷换鍔″垎閰嶉€氱煡锛屽垎閰嶇粰锛歿}锛屼换鍔★細{}锛岀敵璇稩D锛歿}",
                    assigneeId, taskName, application.getId());

            // 鏋勫缓閫氱煡鍐呭
            Map<String, Object> params = new HashMap<>();
            params.put("taskName", taskName);
            params.put("visitorName", application.getVisitorName());
            params.put("applicationNo", application.getApplicationNo());
            params.put("visitReason", application.getVisitReason());

            // 鍙戦€佺煭淇￠€氱煡锛堥渶瑕佽幏鍙栫敤鎴锋墜鏈哄彿锛?            // TODO: 鏍规嵁assigneeId鑾峰彇鐢ㄦ埛鎵嬫満鍙峰苟鍙戦€佺煭淇?            log.info("[sendTaskAssignmentNotification] 浠诲姟鍒嗛厤閫氱煡鍐呭锛歿}", params);

        } catch (Exception e) {
            log.error("[sendTaskAssignmentNotification] 鍙戦€佷换鍔″垎閰嶉€氱煡失败", e);
        }
    }

    /**
     * 鍙戦€佷换鍔″畬鎴愰€氱煡
     */
    public void sendTaskCompletionNotification(String taskName, VisitorApplicationDO application) {
        try {
            log.info("[sendTaskCompletionNotification] 鍙戦€佷换鍔″畬鎴愰€氱煡锛屼换鍔★細{}锛岀敵璇稩D锛歿}",
                    taskName, application.getId());

            // 鏋勫缓閫氱煡鍐呭
            Map<String, Object> params = new HashMap<>();
            params.put("taskName", taskName);
            params.put("visitorName", application.getVisitorName());
            params.put("applicationNo", application.getApplicationNo());

            // 鍙戦€佺煭淇￠€氱煡缁欑敵璇蜂汉
            if (StrUtil.isNotBlank(application.getVisitorPhone())) {
                sendSmsToVisitor(application.getVisitorPhone(), "VISITOR_TASK_COMPLETED", params);
            }

        } catch (Exception e) {
            log.error("[sendTaskCompletionNotification] 鍙戦€佷换鍔″畬鎴愰€氱煡失败", e);
        }
    }

}

