﻿package cn.iocoder.yudao.module.visitor.controller.admin.application.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 管理后台 - 访客入园登记 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客入园登记 Request VO")
@Data
public class VisitorApplicationEntryReqVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "申请ID不能为空")
    private Long id;

    @Schema(description = "操作员業D", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "操作员業D不能为空")
    private Long operatorId;

    @Schema(description = "门岗浣嶇疆", requiredMode = Schema.RequiredMode.REQUIRED, example = "涓滈棬")
    @NotNull(message = "门岗浣嶇疆不能为空")
    private String gateLocation;

    @Schema(description = "验证鏂瑰紡", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "验证鏂瑰紡不能为空")
    private Integer verificationMethod;

    @Schema(description = "浣撴俯", example = "36.5")
    private Double temperature;

    @Schema(description = "备注", example = "正常入园")
    private String remarks;

}

