﻿package cn.iocoder.yudao.module.visitor.controller.app.training.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 鐢ㄦ埛 APP - 培训完成 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Schema(description = "鐢ㄦ埛 APP - 培训完成 Request VO")
@Data
public class AppTrainingCompleteReqVO {

    @Schema(description = "培训记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "培训记录ID不能为空")
    private Long recordId;

    @Schema(description = "考试分数", example = "85")
    private Integer examScore;

    @Schema(description = "考试绛旀", example = "{\"q1\":\"A\",\"q2\":\"B\"}")
    private Object examAnswers;

    @Schema(description = "绛惧瓧鍥剧墖URL", example = "https://example.com/signature.jpg")
    private String signatureImage;

}

