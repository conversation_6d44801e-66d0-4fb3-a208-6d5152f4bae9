﻿package cn.iocoder.yudao.module.visitor.controller.app.guard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 鐢ㄦ埛 APP - 二维码佹壂鎻?Request VO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Schema(description = "鐢ㄦ埛 APP - 二维码佹壂鎻?Request VO")
@Data
public class AppQrCodeScanReqVO {

    @Schema(description = "二维码佸唴瀹?, requiredMode = Schema.RequiredMode.REQUIRED, example = "eyJhbGciOiJIUzI1NiJ9...")
    @NotBlank(message = "二维码佸唴瀹逛笉鑳戒负绌?)
    private String qrCodeContent;

    @Schema(description = "门岗浣嶇疆", example = "涓滈棬")
    private String gateLocation;

    @Schema(description = "鎵弿璁惧ID", example = "GATE_001")
    private String deviceId;

}

