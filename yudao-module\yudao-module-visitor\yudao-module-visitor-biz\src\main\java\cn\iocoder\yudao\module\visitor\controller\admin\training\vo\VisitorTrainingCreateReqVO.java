﻿package cn.iocoder.yudao.module.visitor.controller.admin.training.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 管理后台 - 访客培训创建 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Schema(description = "管理后台 - 访客培训创建 Request VO")
@Data
public class VisitorTrainingCreateReqVO {

    @Schema(description = "培训名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "安全培训")
    @NotBlank(message = "培训名称不能为空")
    private String trainingName;

    @Schema(description = "培训类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "培训类型不能为空")
    private Integer trainingType;

    @Schema(description = "閫傜敤访客类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "閫傜敤访客类型不能为空")
    private Integer visitorType;

    @Schema(description = "培训鍐呭", example = "安全操作员瑙勭▼...")
    private String trainingContent;

    @Schema(description = "培训瑙嗛URL", example = "https://example.com/video.mp4")
    private String trainingVideoUrl;

    @Schema(description = "培训鏂囨。URL", example = "https://example.com/doc.pdf")
    private String trainingDocumentUrl;

    @Schema(description = "最小忓煿璁椂闀匡紙分钟锛?, example = "30")
    private Integer minDuration;

    @Schema(description = "鏄惁鏈夎€冭瘯", example = "true")
    private Boolean hasExam;

    @Schema(description = "考试棰樼洰JSON", example = "[{\"question\":\"...\",\"options\":[...]}]")
    private String examQuestions;

    @Schema(description = "鍙婃牸分数", example = "80")
    private Integer passScore;

    @Schema(description = "鏄惁必修", example = "true")
    private Boolean isRequired;

    @Schema(description = "鎺掑簭", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态?, example = "1")
    private Integer status;

}

